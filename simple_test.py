#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
简单测试 parse_first_option 函数
"""

import re

def parse_first_option(text: str) -> str:
    """
    Find first valid option for text.

    Args:
        text: The text to parse.
    """
    # 添加调试信息
    print(f"[DEBUG] parse_first_option 被调用")
    print(f"[DEBUG] 输入文本长度: {len(text)}")
    print(f"[DEBUG] 输入文本前200字符: {repr(text[:200])}")
    
    patterns = [
        r'[Aa]nswer:\s*(\w+)',
        r'ANSWER:\s*(\w+)',
        r'answer is \(?(\w+)\)?',
        r'[Tt]he correct answer is:\s*(\w+)',
        r'[Tt]he correct answer is:\n\s*(\w+)',
        r'[Tt]he correct answer is:\n\n-\s*(\w+)',
        r'[Tt]he answer might be:\n\n-\s*(\w+)',
        r'[Tt]he answer is \s*(\w+)',
        r'answer is \(?([A-Za-z0-9]+)\)?',
        ] 

    print(f"[DEBUG] 总共有 {len(patterns)} 个匹配模式")
    
    regexes = [re.compile(pattern) for pattern in patterns]
    for i, regex in enumerate(regexes):
        print(f"[DEBUG] 尝试模式 {i+1}: {patterns[i]}")
        match = regex.search(text)
        if match:
            result = match.group(1)
            print(f"[DEBUG] 模式 {i+1} 匹配成功! 匹配到: {repr(match.group(0))}")
            print(f"[DEBUG] 提取的答案: {repr(result)}")
            return result
        else:
            print(f"[DEBUG] 模式 {i+1} 未匹配")
    
    print(f"[DEBUG] 所有模式都未匹配，返回空字符串")
    return ''

def test_function():
    """测试函数"""
    
    print("=" * 60)
    print("开始测试 parse_first_option 函数")
    print("=" * 60)
    
    # 测试用例
    test_cases = [
        # 英文格式测试
        "Answer: A",
        "ANSWER: B", 
        "The answer is C",
        "The correct answer is: D",
        "The correct answer is:\nA",
        "The correct answer is:\n\n- B",
        "The answer might be:\n\n- C",
        "answer is (D)",
        "answer is A",
        
        # 复杂文本测试
        "Based on the analysis above, the answer is A. This is because...",
        "After careful consideration, I believe the correct answer is: B",
        "Looking at all the options, answer is (C)",
        
        # 无匹配测试
        "这是一段没有答案的文本",
        "No clear answer here",
        "",
        
        # 边界情况测试
        "Multiple answers: A, B, C, but the answer is D",
        "answer is 123",  # 数字答案
        "answer is ABC",  # 多字符答案
    ]
    
    print(f"总共 {len(test_cases)} 个测试用例\n")
    
    for i, test_text in enumerate(test_cases, 1):
        print(f"\n{'='*50}")
        print(f"测试用例 {i}:")
        print(f"输入: {repr(test_text)}")
        print(f"{'='*50}")
        
        try:
            result = parse_first_option(test_text)
            print(f"最终结果: {repr(result)}")
            if result:
                print(f"✓ 成功提取到答案: {result}")
            else:
                print("✗ 未提取到答案")
        except Exception as e:
            print(f"❌ 发生错误: {e}")
        
        print("-" * 50)

if __name__ == "__main__":
    print("开始测试...")
    test_function()
    print("\n测试完成!")
