/* Excel表格页面样式 */
.excel-table-page {
  padding: 16px;
  background: #f5f5f5;
  min-height: 100vh;
  user-select: none;
}

.excel-table-page .ant-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e8e8e8;
}

/* 页面头部 */
.page-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #1f1f1f;
}

.page-icon {
  color: #52c41a;
  font-size: 18px;
}

.unsaved-indicator {
  color: #ff4d4f;
  font-size: 20px;
  margin-left: 4px;
  animation: pulse 1.5s infinite;
}

/* 状态信息 */
.status-info {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 2px;
  font-size: 12px;
  color: #666;
  margin-right: 8px;
}

.selection-info {
  padding: 2px 6px;
  background: #e6f7ff;
  border: 1px solid #91d5ff;
  border-radius: 4px;
  font-size: 11px;
  color: #1890ff;
  white-space: nowrap;
}

/* 帮助信息 */
.help-info {
  margin-bottom: 16px;
  padding: 12px 16px;
  background: linear-gradient(135deg, #f0f9ff 0%, #e6f7ff 100%);
  border: 1px solid #91d5ff;
  border-radius: 6px;
}

.help-shortcuts {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  font-size: 12px;
  color: #666;
}

.help-shortcuts kbd {
  padding: 2px 4px;
  background: #f5f5f5;
  border: 1px solid #ddd;
  border-radius: 3px;
  font-family: 'Courier New', monospace;
  font-size: 11px;
  color: #333;
  white-space: nowrap;
}

/* Excel表格容器 */
.excel-table-container {
  border: 2px solid #e8e8e8;
  border-radius: 4px;
  background: #fff;
  overflow: auto;
  max-height: 70vh;
  position: relative;
  user-select: none;
}

/* 表格头部 */
.excel-header {
  display: flex;
  background: #f5f5f5;
  border-bottom: 2px solid #d9d9d9;
  position: sticky;
  top: 0;
  z-index: 10;
}

.excel-corner {
  width: 50px;
  min-width: 50px;
  height: 32px;
  background: #fafafa;
  border-right: 1px solid #d9d9d9;
  border-bottom: 1px solid #d9d9d9;
}

.excel-header-cell {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 32px;
  padding: 0 8px;
  border-right: 1px solid #d9d9d9;
  border-bottom: 1px solid #d9d9d9;
  background: #fafafa;
  font-weight: 600;
  font-size: 12px;
  color: #333;
  cursor: pointer;
  user-select: none;
  position: relative;
  transition: background-color 0.2s;
}

.excel-header-cell:hover {
  background: #e6f7ff;
}

.excel-header-cell.sorted {
  background: #bae7ff;
  color: #1890ff;
}

.required-mark {
  color: #ff4d4f;
  margin-left: 2px;
}

.sort-icon {
  margin-left: 4px;
  color: #1890ff;
  font-weight: bold;
}

/* 表格主体 */
.excel-body {
  background: #fff;
  position: relative;
  min-height: 300px;
}

.excel-row {
  display: flex;
  border-bottom: 1px solid #f0f0f0;
  transition: background-color 0.1s;
  min-height: 28px;
}

.excel-row:hover {
  background: rgba(24, 144, 255, 0.02);
}

.excel-row.selected {
  background: rgba(24, 144, 255, 0.1);
}

.excel-row.modified {
  border-left: 3px solid #faad14;
  background: rgba(250, 173, 20, 0.05);
}

/* 行号 */
.excel-row-number {
  width: 50px;
  min-width: 50px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #fafafa;
  border-right: 1px solid #d9d9d9;
  font-size: 11px;
  color: #666;
  font-weight: 500;
  cursor: pointer;
  user-select: none;
  transition: background-color 0.2s;
  position: sticky;
  left: 0;
  z-index: 8;
}

.excel-row-number:hover {
  background: #e6f7ff;
}

.excel-row.selected .excel-row-number {
  background: #1890ff;
  color: #fff;
}

/* 单元格 */
.excel-cell {
  height: 28px;
  border-right: 1px solid #f0f0f0;
  display: flex;
  align-items: center;
  padding: 0;
  position: relative;
  cursor: cell;
  user-select: none;
  transition: all 0.1s;
  background: #fff;
}

.excel-cell:hover {
  background: rgba(24, 144, 255, 0.05);
}

.excel-cell.selected {
  background: rgba(24, 144, 255, 0.15);
  border: 2px solid #1890ff;
  z-index: 5;
}

.excel-cell.active {
  background: rgba(24, 144, 255, 0.25);
  border: 3px solid #1890ff;
  z-index: 6;
  box-shadow: 0 0 0 1px #1890ff;
}

.excel-cell.editing {
  background: #fff;
  border: 3px solid #52c41a;
  z-index: 7;
  box-shadow: 0 0 0 1px #52c41a;
}

.excel-cell-content {
  width: 100%;
  height: 100%;
  padding: 0 8px;
  display: flex;
  align-items: center;
  font-size: 12px;
  color: #333;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.excel-cell-input {
  width: 100%;
  height: 100%;
  border: none;
  outline: none;
  padding: 0 8px;
  font-size: 12px;
  color: #333;
  background: #fff;
  font-family: inherit;
}

.excel-cell-input:focus {
  outline: none;
  box-shadow: none;
}

/* 加载状态 */
.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #666;
}

.loading-spinner {
  font-size: 14px;
  color: #1890ff;
  animation: pulse 1.5s infinite;
}

/* 批量操作菜单 */
.batch-actions-menu {
  display: flex;
  flex-direction: column;
  gap: 4px;
  padding: 8px 0;
  min-width: 150px;
}

.batch-actions-menu .ant-btn {
  justify-content: flex-start;
  height: 32px;
  padding: 0 12px;
  border-radius: 4px;
  border: none;
  background: transparent;
  color: #333;
  text-align: left;
}

.batch-actions-menu .ant-btn:hover {
  background: #f5f5f5;
  color: #1890ff;
}

.batch-actions-menu .ant-btn.ant-btn-dangerous {
  color: #ff4d4f;
}

.batch-actions-menu .ant-btn.ant-btn-dangerous:hover {
  background: #fff2f0;
  color: #ff4d4f;
}

/* 右键菜单样式 */
.ant-dropdown {
  z-index: 1001;
}

.ant-dropdown .ant-dropdown-menu {
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border: 1px solid #e8e8e8;
  min-width: 140px;
}

.ant-dropdown .ant-dropdown-menu-item {
  padding: 8px 12px;
  font-size: 12px;
  transition: all 0.2s;
}

.ant-dropdown .ant-dropdown-menu-item:hover {
  background: #f5f5f5;
}

.ant-dropdown .ant-dropdown-menu-item-disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.ant-dropdown .ant-dropdown-menu-item-disabled:hover {
  background: transparent;
}

.ant-dropdown .ant-dropdown-menu-item-danger {
  color: #ff4d4f;
}

.ant-dropdown .ant-dropdown-menu-item-danger:hover {
  background: #fff2f0;
}

/* 响应式设计 */
@media (max-width: 1400px) {
  .help-shortcuts {
    gap: 12px;
  }
  
  .status-info {
    flex-direction: row;
    gap: 8px;
  }
}

@media (max-width: 1200px) {
  .excel-table-page {
    padding: 12px;
  }
  
  .help-shortcuts {
    gap: 8px;
  }
  
  .help-shortcuts span {
    font-size: 11px;
  }
}

@media (max-width: 768px) {
  .excel-table-page {
    padding: 8px;
  }
  
  .page-header {
    font-size: 14px;
  }
  
  .excel-table-container {
    max-height: 60vh;
  }
  
  .help-info {
    padding: 8px 12px;
  }
  
  .help-shortcuts {
    flex-direction: column;
    gap: 4px;
  }
  
  .status-info {
    display: none;
  }
  
  .excel-header-cell,
  .excel-cell {
    min-width: 80px;
  }
}

/* 动画效果 */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* 选择高亮效果 */
.excel-cell.selected::after {
  content: '';
  position: absolute;
  top: -1px;
  right: -1px;
  width: 6px;
  height: 6px;
  background: #1890ff;
  cursor: se-resize;
  z-index: 10;
}

.excel-cell.active::after {
  content: '';
  position: absolute;
  top: -2px;
  right: -2px;
  width: 8px;
  height: 8px;
  background: #1890ff;
  cursor: se-resize;
  z-index: 11;
  border: 1px solid #fff;
}

/* 滚动条样式 */
.excel-table-container::-webkit-scrollbar {
  width: 12px;
  height: 12px;
}

.excel-table-container::-webkit-scrollbar-track {
  background: #f5f5f5;
  border-radius: 6px;
}

.excel-table-container::-webkit-scrollbar-thumb {
  background: #d9d9d9;
  border-radius: 6px;
  border: 2px solid #f5f5f5;
}

.excel-table-container::-webkit-scrollbar-thumb:hover {
  background: #bfbfbf;
}

.excel-table-container::-webkit-scrollbar-corner {
  background: #f5f5f5;
}

/* 数字单元格右对齐 */
.excel-cell[data-type="number"] .excel-cell-content {
  justify-content: flex-end;
  text-align: right;
}

.excel-cell[data-type="number"] .excel-cell-input {
  text-align: right;
}

/* 冻结列样式 */
.excel-header-cell[data-fixed="left"],
.excel-cell[data-fixed="left"] {
  position: sticky;
  left: 50px;
  background: #fff;
  z-index: 9;
  border-left: 1px solid #d9d9d9;
}

.excel-header-cell[data-fixed="left"] {
  background: #fafafa;
  z-index: 12;
}

.excel-row.selected .excel-cell[data-fixed="left"] {
  background: rgba(24, 144, 255, 0.1);
}

.excel-cell[data-fixed="left"].selected {
  background: rgba(24, 144, 255, 0.15);
}

.excel-cell[data-fixed="left"].active {
  background: rgba(24, 144, 255, 0.25);
}

/* 表格边框加强 */
.excel-table-container {
  border: 2px solid #d9d9d9;
}

.excel-header {
  border-bottom: 2px solid #d9d9d9;
}

.excel-cell {
  border-right: 1px solid #e8e8e8;
}

.excel-row {
  border-bottom: 1px solid #e8e8e8;
}

.excel-row:last-child {
  border-bottom: 1px solid #d9d9d9;
}

/* 性能优化：使用transform代替位置变化 */
.excel-cell.selected {
  transform: scale(1);
  transform-origin: center;
}

/* 工具提示样式增强 */
.ant-tooltip {
  z-index: 1002;
}

.ant-tooltip .ant-tooltip-inner {
  font-size: 12px;
  padding: 4px 8px;
}

/* 按钮组样式优化 */
.excel-table-page .ant-space {
  flex-wrap: wrap;
}

.excel-table-page .ant-btn-sm {
  height: 28px;
  padding: 0 8px;
  font-size: 12px;
  border-radius: 4px;
}

.excel-table-page .ant-btn-sm .anticon {
  font-size: 12px;
}

/* 卡片样式优化 */
.excel-table-page .ant-card-head {
  padding: 0 16px;
  min-height: 48px;
  border-bottom: 1px solid #e8e8e8;
}

.excel-table-page .ant-card-body {
  padding: 16px;
}

.excel-table-page .ant-card-extra {
  color: #666;
}

/* 性能测试数据特定样式 */
.performance-indicator {
  display: inline-block;
  padding: 2px 6px;
  border-radius: 10px;
  font-size: 10px;
  font-weight: 500;
  margin-left: 4px;
}

.performance-indicator.good {
  background: #f6ffed;
  color: #52c41a;
  border: 1px solid #b7eb8f;
}

.performance-indicator.warning {
  background: #fff7e6;
  color: #fa8c16;
  border: 1px solid #ffd591;
}

.performance-indicator.poor {
  background: #fff2f0;
  color: #ff4d4f;
  border: 1px solid #ffb3b3;
}

/* 键盘焦点样式 */
.excel-cell:focus-within {
  outline: 2px solid #1890ff;
  outline-offset: -2px;
}

/* 选择区域拖拽样式 */
.excel-cell.drag-selecting {
  background: rgba(24, 144, 255, 0.1);
  border: 1px dashed #1890ff;
}

/* 数据验证错误样式 */
.excel-cell.validation-error {
  background: rgba(255, 77, 79, 0.1);
  border-color: #ff4d4f;
}

.excel-cell.validation-error .excel-cell-content {
  color: #ff4d4f;
}

/* 空数据提示 */
.empty-data-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
  color: #999;
  font-size: 14px;
}

.empty-data-placeholder .anticon {
  font-size: 48px;
  margin-bottom: 16px;
  color: #d9d9d9;
}

/* 自动保存指示器 */
.autosave-indicator {
  position: fixed;
  top: 20px;
  right: 20px;
  background: #1890ff;
  color: #fff;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 12px;
  z-index: 1000;
  animation: slideInRight 0.3s ease;
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .excel-cell {
    border-color: #000;
  }
  
  .excel-cell.selected {
    background: rgba(0, 0, 255, 0.3);
    border-color: #000;
  }
  
  .excel-cell.active {
    background: rgba(0, 0, 255, 0.5);
    border-color: #000;
  }
}

/* 减少动画模式支持 */
@media (prefers-reduced-motion: reduce) {
  .excel-cell,
  .excel-row,
  .excel-header-cell {
    transition: none;
  }
  
  .unsaved-indicator,
  .loading-spinner {
    animation: none;
  }
} 